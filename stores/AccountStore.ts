import { authService } from "@/services/authService";
import { makeAutoObservable, runInAction } from "mobx";

export interface SocialAccount {
    id: string;
    platform: "facebook" | "instagram" | "twitter";
    username: string;
    displayName: string;
    email?: string;
    avatar?: string;
    isConnected: boolean;
    accessToken?: string;
    refreshToken?: string;
    expiresAt?: Date;
    connectedAt?: Date;
}

export interface PlatformConfig {
    platform: "facebook" | "instagram" | "twitter";
    clientId: string;
    clientSecret: string;
    redirectUri?: string;
    scopes: string[];
}

class AccountStore {
    accounts: Map<string, SocialAccount> = new Map();
    platformConfigs: Map<string, PlatformConfig> = new Map();
    isLoading = false;
    error: string | null = null;

    constructor() {
        makeAutoObservable(this);
        this.loadStoredAccounts();
    }

    // Load accounts from storage on app start
    async loadStoredAccounts() {
        this.setLoading(true);
        try {
            const platforms = ["facebook", "instagram", "twitter"];

            for (const platform of platforms) {
                const token = await authService.getStoredToken(platform);
                if (token) {
                    const profile = await authService.getUserProfile(platform);
                    if (profile) {
                        const account: SocialAccount = {
                            id: profile.id,
                            platform: profile.platform,
                            username: profile.username,
                            displayName: profile.name,
                            email: profile.email,
                            avatar: profile.avatar,
                            isConnected: true,
                            accessToken: token.accessToken,
                            refreshToken: token.refreshToken,
                            connectedAt: new Date(),
                        };
                        this.accounts.set(platform, account);
                    }
                }
            }
        } catch (error) {
            this.setError(error instanceof Error ? error.message : "Failed to load accounts");
        } finally {
            this.setLoading(false);
        }
    }

    // Set platform configuration
    setPlatformConfig(config: PlatformConfig) {
        this.platformConfigs.set(config.platform, config);

        // Update authService configuration
        authService.updatePlatformConfig(config.platform, {
            clientId: config.clientId,
            scopes: config.scopes,
        });
    }

    // Get platform configuration
    getPlatformConfig(platform: string): PlatformConfig | undefined {
        return this.platformConfigs.get(platform);
    }

    // Connect account
    async connectAccount(platform: "facebook" | "instagram" | "twitter") {
        this.setLoading(true);
        this.setError(null);

        try {
            let token;

            switch (platform) {
                case "facebook":
                    token = await authService.authenticateFacebook();
                    break;
                case "instagram":
                    token = await authService.authenticateInstagram();
                    break;
                case "twitter":
                    token = await authService.authenticateTwitter();
                    break;
                default:
                    throw new Error("Unsupported platform");
            }

            // Get user profile
            const profile = await authService.getUserProfile(platform);
            if (!profile) {
                throw new Error("Failed to get user profile");
            }

            const account: SocialAccount = {
                id: profile.id,
                platform: profile.platform,
                username: profile.username,
                displayName: profile.name,
                email: profile.email,
                avatar: profile.avatar,
                isConnected: true,
                accessToken: token.accessToken,
                refreshToken: token.refreshToken,
                connectedAt: new Date(),
            };

            runInAction(() => {
                this.accounts.set(platform, account);
            });

            return account;
        } catch (error) {
            this.setError(error instanceof Error ? error.message : "Failed to connect account");
            throw error;
        } finally {
            this.setLoading(false);
        }
    }

    // Disconnect account
    async disconnectAccount(platform: string) {
        this.setLoading(true);
        this.setError(null);

        try {
            await authService.logout(platform);

            runInAction(() => {
                this.accounts.delete(platform);
            });
        } catch (error) {
            this.setError(error instanceof Error ? error.message : "Failed to disconnect account");
            throw error;
        } finally {
            this.setLoading(false);
        }
    }

    // Check if platform is connected
    isConnected(platform: string): boolean {
        const account = this.accounts.get(platform);
        return account?.isConnected || false;
    }

    // Get account by platform
    getAccount(platform: string): SocialAccount | undefined {
        return this.accounts.get(platform);
    }

    // Get all connected accounts
    get connectedAccounts(): SocialAccount[] {
        return Array.from(this.accounts.values()).filter((account) => account.isConnected);
    }

    // Get connected platforms
    get connectedPlatforms(): string[] {
        return this.connectedAccounts.map((account) => account.platform);
    }

    // Get connection count
    get connectionCount(): number {
        return this.connectedAccounts.length;
    }

    // Update account info
    updateAccount(platform: string, updates: Partial<SocialAccount>) {
        const account = this.accounts.get(platform);
        if (account) {
            runInAction(() => {
                this.accounts.set(platform, { ...account, ...updates });
            });
        }
    }

    // Refresh account token
    async refreshAccountToken(platform: string) {
        const account = this.accounts.get(platform);
        if (!account || !account.refreshToken) {
            throw new Error("No refresh token available");
        }

        this.setLoading(true);
        try {
            // This would implement actual token refresh logic
            // For now, just update the timestamp
            this.updateAccount(platform, {
                connectedAt: new Date(),
            });
        } catch (error) {
            this.setError(error instanceof Error ? error.message : "Failed to refresh token");
            throw error;
        } finally {
            this.setLoading(false);
        }
    }

    // Private methods
    private setLoading(loading: boolean) {
        this.isLoading = loading;
    }

    private setError(error: string | null) {
        this.error = error;
    }

    // Clear error
    clearError() {
        this.error = null;
    }

    // Reset store
    reset() {
        this.accounts.clear();
        this.platformConfigs.clear();
        this.isLoading = false;
        this.error = null;
    }
}

export const accountStore = new AccountStore();
