import { makeAutoObservable, runInAction } from 'mobx';
import { socialMediaAPI, SocialPost } from '@/services/socialMediaAPI';
import { accountStore } from './AccountStore';

class FeedStore {
  posts: SocialPost[] = [];
  isLoading = false;
  isRefreshing = false;
  error: string | null = null;
  lastFetchTime: Date | null = null;
  hasMore = true;
  nextPageTokens: Map<string, string> = new Map();

  constructor() {
    makeAutoObservable(this);
  }

  // Load initial feed
  async loadFeed(limit: number = 50) {
    if (this.isLoading) return;

    this.setLoading(true);
    this.setError(null);

    try {
      const response = await socialMediaAPI.getCombinedFeed(limit);
      
      runInAction(() => {
        if (response.success) {
          this.posts = response.data;
          this.lastFetchTime = new Date();
          
          // Store next page tokens if available
          if (response.nextPageToken) {
            this.nextPageTokens.set('combined', response.nextPageToken);
          }
        } else {
          this.setError(response.error || 'Failed to load feed');
        }
      });
    } catch (error) {
      this.setError(error instanceof Error ? error.message : 'Failed to load feed');
    } finally {
      this.setLoading(false);
    }
  }

  // Refresh feed
  async refreshFeed() {
    if (this.isRefreshing) return;

    this.setRefreshing(true);
    this.setError(null);

    try {
      const response = await socialMediaAPI.getCombinedFeed(50);
      
      runInAction(() => {
        if (response.success) {
          this.posts = response.data;
          this.lastFetchTime = new Date();
          this.nextPageTokens.clear();
          
          if (response.nextPageToken) {
            this.nextPageTokens.set('combined', response.nextPageToken);
          }
        } else {
          this.setError(response.error || 'Failed to refresh feed');
        }
      });
    } catch (error) {
      this.setError(error instanceof Error ? error.message : 'Failed to refresh feed');
    } finally {
      this.setRefreshing(false);
    }
  }

  // Load more posts (pagination)
  async loadMorePosts() {
    if (this.isLoading || !this.hasMore) return;

    this.setLoading(true);
    
    try {
      // This would implement pagination logic
      // For now, just simulate loading more posts
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      runInAction(() => {
        this.hasMore = false; // No more posts for demo
      });
    } catch (error) {
      this.setError(error instanceof Error ? error.message : 'Failed to load more posts');
    } finally {
      this.setLoading(false);
    }
  }

  // Refresh posts from specific platform
  async refreshPlatformPosts(platform: 'facebook' | 'instagram' | 'twitter') {
    if (!accountStore.isConnected(platform)) {
      this.setError(`${platform} account is not connected`);
      return;
    }

    this.setLoading(true);
    this.setError(null);

    try {
      const response = await socialMediaAPI.refreshPlatformPosts(platform);
      
      if (response.success) {
        runInAction(() => {
          // Remove old posts from this platform
          this.posts = this.posts.filter(post => post.platform !== platform);
          
          // Add new posts and sort by timestamp
          this.posts.push(...response.data);
          this.posts.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
          
          this.lastFetchTime = new Date();
        });
      } else {
        this.setError(response.error || `Failed to refresh ${platform} posts`);
      }
    } catch (error) {
      this.setError(error instanceof Error ? error.message : `Failed to refresh ${platform} posts`);
    } finally {
      this.setLoading(false);
    }
  }

  // Get posts by platform
  getPostsByPlatform(platform: string): SocialPost[] {
    return this.posts.filter(post => post.platform === platform);
  }

  // Get posts count by platform
  getPostsCountByPlatform(platform: string): number {
    return this.getPostsByPlatform(platform).length;
  }

  // Get total posts count
  get totalPostsCount(): number {
    return this.posts.length;
  }

  // Get platforms with posts
  get platformsWithPosts(): string[] {
    const platforms = new Set(this.posts.map(post => post.platform));
    return Array.from(platforms);
  }

  // Check if feed is empty
  get isEmpty(): boolean {
    return this.posts.length === 0 && !this.isLoading;
  }

  // Check if feed needs refresh (older than 5 minutes)
  get needsRefresh(): boolean {
    if (!this.lastFetchTime) return true;
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    return this.lastFetchTime < fiveMinutesAgo;
  }

  // Add new post (for real-time updates)
  addPost(post: SocialPost) {
    runInAction(() => {
      this.posts.unshift(post);
      // Keep only latest 100 posts to prevent memory issues
      if (this.posts.length > 100) {
        this.posts = this.posts.slice(0, 100);
      }
    });
  }

  // Remove post
  removePost(postId: string) {
    runInAction(() => {
      this.posts = this.posts.filter(post => post.id !== postId);
    });
  }

  // Update post (for like/comment updates)
  updatePost(postId: string, updates: Partial<SocialPost>) {
    runInAction(() => {
      const index = this.posts.findIndex(post => post.id === postId);
      if (index !== -1) {
        this.posts[index] = { ...this.posts[index], ...updates };
      }
    });
  }

  // Private methods
  private setLoading(loading: boolean) {
    this.isLoading = loading;
  }

  private setRefreshing(refreshing: boolean) {
    this.isRefreshing = refreshing;
  }

  private setError(error: string | null) {
    this.error = error;
  }

  // Clear error
  clearError() {
    this.error = null;
  }

  // Reset store
  reset() {
    this.posts = [];
    this.isLoading = false;
    this.isRefreshing = false;
    this.error = null;
    this.lastFetchTime = null;
    this.hasMore = true;
    this.nextPageTokens.clear();
  }
}

export const feedStore = new FeedStore();
