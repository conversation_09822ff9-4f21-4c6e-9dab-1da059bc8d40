const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Exclude Instagram scraper from web builds
config.resolver.platforms = ['ios', 'android', 'native', 'web'];
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// Platform-specific module resolution
config.resolver.platformExtensions = ['web.js', 'web.ts', 'web.tsx', 'js', 'ts', 'tsx'];

// Exclude problematic packages from web builds
if (process.env.EXPO_PLATFORM === 'web') {
  config.resolver.blockList = [
    /.*\/instagramScraper\.ts$/,
    /.*\/cheerio\/.*$/,
    /.*\/tough-cookie\/.*$/,
  ];
}

module.exports = config;
