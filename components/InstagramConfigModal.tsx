import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Linking,
} from 'react-native';
import { Colors } from '@/constants/Colors';
import { Typography, Spacing, BorderRadius } from '@/constants/Typography';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { IconSymbol } from '@/components/ui/IconSymbol';

interface InstagramConfigModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (config: InstagramConfig) => void;
}

export interface InstagramConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
}

export function InstagramConfigModal({ visible, onClose, onSave }: InstagramConfigModalProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  const [config, setConfig] = useState<InstagramConfig>({
    clientId: '',
    clientSecret: '',
    redirectUri: 'myfirstexpoapp://auth/instagram',
  });
  
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async () => {
    if (!config.clientId.trim() || !config.clientSecret.trim()) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    setIsLoading(true);
    try {
      onSave(config);
      onClose();
    } catch (error) {
      Alert.alert('Error', 'Failed to save configuration');
    } finally {
      setIsLoading(false);
    }
  };

  const openInstagramDeveloperDocs = () => {
    Linking.openURL('https://developers.facebook.com/docs/instagram-basic-display-api/getting-started');
  };

  const openInstagramAppCreation = () => {
    Linking.openURL('https://developers.facebook.com/apps/');
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={[styles.header, { borderBottomColor: colors.border }]}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <IconSymbol name="xmark" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.title, { color: colors.text }]}>
            Instagram Configuration
          </Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <Card style={styles.infoCard}>
            <View style={styles.infoHeader}>
              <IconSymbol name="info.circle.fill" size={24} color={colors.primary} />
              <Text style={[styles.infoTitle, { color: colors.text }]}>
                Setup Required
              </Text>
            </View>
            <Text style={[styles.infoText, { color: colors.textSecondary }]}>
              To connect your Instagram account, you need to create an Instagram Basic Display app 
              and get your Client ID and Client Secret.
            </Text>
          </Card>

          <Card style={styles.stepsCard}>
            <Text style={[styles.stepsTitle, { color: colors.text }]}>
              Setup Steps:
            </Text>
            
            <View style={styles.step}>
              <View style={[styles.stepNumber, { backgroundColor: colors.primary }]}>
                <Text style={styles.stepNumberText}>1</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={[styles.stepTitle, { color: colors.text }]}>
                  Create Instagram App
                </Text>
                <Text style={[styles.stepDescription, { color: colors.textSecondary }]}>
                  Go to Facebook Developers and create a new app with Instagram Basic Display product.
                </Text>
                <TouchableOpacity onPress={openInstagramAppCreation}>
                  <Text style={[styles.link, { color: colors.primary }]}>
                    Open Facebook Developers →
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.step}>
              <View style={[styles.stepNumber, { backgroundColor: colors.primary }]}>
                <Text style={styles.stepNumberText}>2</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={[styles.stepTitle, { color: colors.text }]}>
                  Configure OAuth Redirect
                </Text>
                <Text style={[styles.stepDescription, { color: colors.textSecondary }]}>
                  Add this redirect URI to your Instagram app settings:
                </Text>
                <View style={[styles.codeBlock, { backgroundColor: colors.backgroundSecondary }]}>
                  <Text style={[styles.codeText, { color: colors.text }]}>
                    {config.redirectUri}
                  </Text>
                </View>
              </View>
            </View>

            <View style={styles.step}>
              <View style={[styles.stepNumber, { backgroundColor: colors.primary }]}>
                <Text style={styles.stepNumberText}>3</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={[styles.stepTitle, { color: colors.text }]}>
                  Get Credentials
                </Text>
                <Text style={[styles.stepDescription, { color: colors.textSecondary }]}>
                  Copy your App ID and App Secret from the Instagram Basic Display settings.
                </Text>
              </View>
            </View>
          </Card>

          <Card style={styles.formCard}>
            <Text style={[styles.formTitle, { color: colors.text }]}>
              App Credentials
            </Text>

            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: colors.text }]}>
                Client ID (App ID) *
              </Text>
              <TextInput
                style={[styles.input, { 
                  backgroundColor: colors.backgroundSecondary,
                  borderColor: colors.border,
                  color: colors.text 
                }]}
                value={config.clientId}
                onChangeText={(text) => setConfig(prev => ({ ...prev, clientId: text }))}
                placeholder="Enter your Instagram App ID"
                placeholderTextColor={colors.textMuted}
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: colors.text }]}>
                Client Secret (App Secret) *
              </Text>
              <TextInput
                style={[styles.input, { 
                  backgroundColor: colors.backgroundSecondary,
                  borderColor: colors.border,
                  color: colors.text 
                }]}
                value={config.clientSecret}
                onChangeText={(text) => setConfig(prev => ({ ...prev, clientSecret: text }))}
                placeholder="Enter your Instagram App Secret"
                placeholderTextColor={colors.textMuted}
                autoCapitalize="none"
                autoCorrect={false}
                secureTextEntry
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: colors.text }]}>
                Redirect URI
              </Text>
              <TextInput
                style={[styles.input, { 
                  backgroundColor: colors.backgroundSecondary,
                  borderColor: colors.border,
                  color: colors.text 
                }]}
                value={config.redirectUri}
                onChangeText={(text) => setConfig(prev => ({ ...prev, redirectUri: text }))}
                placeholder="Redirect URI"
                placeholderTextColor={colors.textMuted}
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>
          </Card>

          <View style={styles.actions}>
            <Button
              title="Need Help?"
              variant="ghost"
              onPress={openInstagramDeveloperDocs}
              style={styles.helpButton}
            />
            <Button
              title="Save & Connect"
              variant="primary"
              onPress={handleSave}
              loading={isLoading}
              style={styles.saveButton}
            />
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
  },
  closeButton: {
    padding: Spacing.sm,
  },
  title: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.semiBold,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: Spacing.lg,
  },
  infoCard: {
    marginBottom: Spacing.lg,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  infoTitle: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.semiBold,
    marginLeft: Spacing.sm,
  },
  infoText: {
    fontSize: Typography.sizes.base,
    lineHeight: Typography.sizes.base * Typography.lineHeights.relaxed,
  },
  stepsCard: {
    marginBottom: Spacing.lg,
  },
  stepsTitle: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.semiBold,
    marginBottom: Spacing.lg,
  },
  step: {
    flexDirection: 'row',
    marginBottom: Spacing.lg,
  },
  stepNumber: {
    width: 32,
    height: 32,
    borderRadius: BorderRadius.full,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  stepNumberText: {
    color: '#ffffff',
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.semiBold,
  },
  stepContent: {
    flex: 1,
  },
  stepTitle: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.semiBold,
    marginBottom: Spacing.xs,
  },
  stepDescription: {
    fontSize: Typography.sizes.sm,
    lineHeight: Typography.sizes.sm * Typography.lineHeights.normal,
    marginBottom: Spacing.sm,
  },
  link: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.medium,
  },
  codeBlock: {
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
    marginTop: Spacing.sm,
  },
  codeText: {
    fontSize: Typography.sizes.sm,
    fontFamily: Typography.fonts.mono,
  },
  formCard: {
    marginBottom: Spacing.lg,
  },
  formTitle: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.semiBold,
    marginBottom: Spacing.lg,
  },
  inputGroup: {
    marginBottom: Spacing.lg,
  },
  label: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.medium,
    marginBottom: Spacing.sm,
  },
  input: {
    borderWidth: 1,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    fontSize: Typography.sizes.base,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: Spacing.xl,
  },
  helpButton: {
    flex: 1,
    marginRight: Spacing.md,
  },
  saveButton: {
    flex: 2,
  },
});
