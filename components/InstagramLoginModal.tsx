import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Colors } from '@/constants/Colors';
import { Typography, Spacing, BorderRadius } from '@/constants/Typography';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { IconSymbol } from '@/components/ui/IconSymbol';

interface InstagramLoginModalProps {
  visible: boolean;
  onClose: () => void;
  onLogin: (username: string, password: string) => Promise<void>;
  loading?: boolean;
}

export function InstagramLoginModal({ 
  visible, 
  onClose, 
  onLogin, 
  loading = false 
}: InstagramLoginModalProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  const [credentials, setCredentials] = useState({
    username: '',
    password: '',
  });
  
  const [showPassword, setShowPassword] = useState(false);

  const handleLogin = async () => {
    if (!credentials.username.trim() || !credentials.password.trim()) {
      Alert.alert('Error', 'Please enter both username and password');
      return;
    }

    try {
      await onLogin(credentials.username, credentials.password);
      setCredentials({ username: '', password: '' });
      onClose();
    } catch (error) {
      Alert.alert(
        'Login Failed', 
        error instanceof Error ? error.message : 'Failed to login to Instagram'
      );
    }
  };

  const handleClose = () => {
    setCredentials({ username: '', password: '' });
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <KeyboardAvoidingView 
        style={[styles.container, { backgroundColor: colors.background }]}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={[styles.header, { borderBottomColor: colors.border }]}>
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <IconSymbol name="xmark" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.title, { color: colors.text }]}>
            Instagram Login
          </Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.content}>
          <Card style={styles.logoCard}>
            <View style={styles.logoContainer}>
              <View style={[styles.instagramIcon, { backgroundColor: colors.instagram }]}>
                <IconSymbol name="camera.fill" size={32} color="#ffffff" />
              </View>
              <Text style={[styles.logoText, { color: colors.text }]}>
                Instagram
              </Text>
              <Text style={[styles.logoSubtext, { color: colors.textSecondary }]}>
                Sign in to your account
              </Text>
            </View>
          </Card>

          <Card style={styles.warningCard}>
            <View style={styles.warningHeader}>
              <IconSymbol name="exclamationmark.triangle.fill" size={20} color={colors.warning} />
              <Text style={[styles.warningTitle, { color: colors.warning }]}>
                Educational Purpose Only
              </Text>
            </View>
            <Text style={[styles.warningText, { color: colors.textSecondary }]}>
              This login method is for educational purposes only. Use at your own risk. 
              We recommend using Instagram's official OAuth for production apps.
            </Text>
          </Card>

          <Card style={styles.formCard}>
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: colors.text }]}>
                Username or Email
              </Text>
              <View style={[styles.inputContainer, { 
                backgroundColor: colors.backgroundSecondary,
                borderColor: colors.border 
              }]}>
                <IconSymbol name="person.fill" size={20} color={colors.textMuted} />
                <TextInput
                  style={[styles.input, { color: colors.text }]}
                  value={credentials.username}
                  onChangeText={(text) => setCredentials(prev => ({ ...prev, username: text }))}
                  placeholder="Enter your username or email"
                  placeholderTextColor={colors.textMuted}
                  autoCapitalize="none"
                  autoCorrect={false}
                  autoComplete="username"
                />
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: colors.text }]}>
                Password
              </Text>
              <View style={[styles.inputContainer, { 
                backgroundColor: colors.backgroundSecondary,
                borderColor: colors.border 
              }]}>
                <IconSymbol name="lock.fill" size={20} color={colors.textMuted} />
                <TextInput
                  style={[styles.input, { color: colors.text }]}
                  value={credentials.password}
                  onChangeText={(text) => setCredentials(prev => ({ ...prev, password: text }))}
                  placeholder="Enter your password"
                  placeholderTextColor={colors.textMuted}
                  secureTextEntry={!showPassword}
                  autoComplete="password"
                />
                <TouchableOpacity 
                  onPress={() => setShowPassword(!showPassword)}
                  style={styles.eyeButton}
                >
                  <IconSymbol 
                    name={showPassword ? "eye.slash.fill" : "eye.fill"} 
                    size={20} 
                    color={colors.textMuted} 
                  />
                </TouchableOpacity>
              </View>
            </View>
          </Card>

          <View style={styles.actions}>
            <Button
              title="Cancel"
              variant="ghost"
              onPress={handleClose}
              style={styles.cancelButton}
            />
            <Button
              title="Sign In"
              variant="primary"
              onPress={handleLogin}
              loading={loading}
              style={styles.loginButton}
            />
          </View>

          <View style={styles.footer}>
            <Text style={[styles.footerText, { color: colors.textMuted }]}>
              Your credentials are used only for this session and are not stored permanently.
            </Text>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
  },
  closeButton: {
    padding: Spacing.sm,
  },
  title: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.semiBold,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: Spacing.lg,
  },
  logoCard: {
    marginBottom: Spacing.lg,
    alignItems: 'center',
  },
  logoContainer: {
    alignItems: 'center',
  },
  instagramIcon: {
    width: 64,
    height: 64,
    borderRadius: BorderRadius.xl,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  logoText: {
    fontSize: Typography.sizes['2xl'],
    fontWeight: Typography.weights.bold,
    marginBottom: Spacing.xs,
  },
  logoSubtext: {
    fontSize: Typography.sizes.base,
  },
  warningCard: {
    marginBottom: Spacing.lg,
  },
  warningHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  warningTitle: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.semiBold,
    marginLeft: Spacing.sm,
  },
  warningText: {
    fontSize: Typography.sizes.sm,
    lineHeight: Typography.sizes.sm * Typography.lineHeights.normal,
  },
  formCard: {
    marginBottom: Spacing.lg,
  },
  inputGroup: {
    marginBottom: Spacing.lg,
  },
  label: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.medium,
    marginBottom: Spacing.sm,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
  },
  input: {
    flex: 1,
    fontSize: Typography.sizes.base,
    marginLeft: Spacing.sm,
    paddingVertical: Spacing.sm,
  },
  eyeButton: {
    padding: Spacing.sm,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.xl,
  },
  cancelButton: {
    flex: 1,
    marginRight: Spacing.md,
  },
  loginButton: {
    flex: 2,
  },
  footer: {
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
  },
  footerText: {
    fontSize: Typography.sizes.xs,
    textAlign: 'center',
    lineHeight: Typography.sizes.xs * Typography.lineHeights.normal,
  },
});
