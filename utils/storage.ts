import { Platform } from "react-native";

// Conditionally import SecureStore only for mobile
let SecureStore: any = null;
if (Platform.OS !== "web") {
    try {
        SecureStore = require("expo-secure-store");
    } catch (error) {
        console.warn("SecureStore not available:", error);
    }
}

// Safe storage wrapper that handles SSR
export const SafeStorage = {
    async getItem(key: string): Promise<string | null> {
        try {
            if (Platform.OS === "web") {
                // Check if we're in browser environment
                if (typeof window !== "undefined" && window.localStorage) {
                    return window.localStorage.getItem(key);
                }
                return null;
            } else {
                // Use SecureStore for mobile
                if (SecureStore) {
                    return await SecureStore.getItemAsync(key);
                }
                return null;
            }
        } catch (error) {
            console.error(`Error getting item ${key}:`, error);
            return null;
        }
    },

    async setItem(key: string, value: string): Promise<void> {
        try {
            if (Platform.OS === "web") {
                // Check if we're in browser environment
                if (typeof window !== "undefined" && window.localStorage) {
                    window.localStorage.setItem(key, value);
                }
                // If SSR, just skip storage
            } else {
                // Use SecureStore for mobile
                if (SecureStore) {
                    await SecureStore.setItemAsync(key, value);
                }
            }
        } catch (error) {
            console.error(`Error setting item ${key}:`, error);
        }
    },

    async removeItem(key: string): Promise<void> {
        try {
            if (Platform.OS === "web") {
                // Check if we're in browser environment
                if (typeof window !== "undefined" && window.localStorage) {
                    window.localStorage.removeItem(key);
                }
                // If SSR, just skip removal
            } else {
                // Use SecureStore for mobile
                if (SecureStore) {
                    await SecureStore.deleteItemAsync(key);
                }
            }
        } catch (error) {
            console.error(`Error removing item ${key}:`, error);
        }
    },
};
