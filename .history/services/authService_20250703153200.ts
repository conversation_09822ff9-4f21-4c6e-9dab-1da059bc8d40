import * as AuthSession from "expo-auth-session";
import * as SecureStore from "expo-secure-store";

// OAuth Configuration - will be set dynamically
let OAUTH_CONFIG = {
    facebook: {
        clientId: "YOUR_FACEBOOK_APP_ID", // Replace with your Facebook App ID
        scopes: ["public_profile", "email", "user_posts"],
        authUrl: "https://www.facebook.com/v18.0/dialog/oauth",
        tokenUrl: "https://graph.facebook.com/v18.0/oauth/access_token",
    },
    instagram: {
        clientId: "", // Will be set from user input
        scopes: ["user_profile", "user_media"],
        authUrl: "https://api.instagram.com/oauth/authorize",
        tokenUrl: "https://api.instagram.com/oauth/access_token",
    },
    twitter: {
        clientId: "YOUR_TWITTER_CLIENT_ID", // Replace with your Twitter Client ID
        scopes: ["tweet.read", "users.read", "offline.access"],
        authUrl: "https://twitter.com/i/oauth2/authorize",
        tokenUrl: "https://api.twitter.com/2/oauth2/token",
    },
};

export interface AuthToken {
    accessToken: string;
    refreshToken?: string;
    expiresIn?: number;
    tokenType?: string;
    scope?: string;
}

export interface UserProfile {
    id: string;
    name: string;
    username: string;
    email?: string;
    avatar?: string;
    platform: "facebook" | "instagram" | "twitter";
}

class AuthService {
    private redirectUri: string;

    constructor() {
        this.redirectUri = AuthSession.makeRedirectUri({
            scheme: "myfirstexpoapp",
            path: "auth",
        });
    }

    // Update OAuth configuration for a platform
    updatePlatformConfig(
        platform: keyof typeof OAUTH_CONFIG,
        config: Partial<(typeof OAUTH_CONFIG)[keyof typeof OAUTH_CONFIG]>
    ) {
        OAUTH_CONFIG[platform] = { ...OAUTH_CONFIG[platform], ...config };
    }

    // Generic OAuth flow
    private async performOAuth(
        platform: keyof typeof OAUTH_CONFIG,
        config: (typeof OAUTH_CONFIG)[keyof typeof OAUTH_CONFIG]
    ): Promise<AuthToken> {
        try {
            const request = new AuthSession.AuthRequest({
                clientId: config.clientId,
                scopes: config.scopes,
                redirectUri: this.redirectUri,
                responseType: AuthSession.ResponseType.Code,
                extraParams: {},
                additionalParameters: {},
                state: AuthSession.AuthRequest.makeRandomState(),
            });

            const result = await request.promptAsync({
                authorizationEndpoint: config.authUrl,
            });

            if (result.type === "success") {
                // Exchange code for token
                const tokenResponse = await this.exchangeCodeForToken(
                    result.params.code,
                    config,
                    platform
                );

                // Store token securely
                await this.storeToken(platform, tokenResponse);

                return tokenResponse;
            } else {
                throw new Error(`OAuth failed: ${result.type}`);
            }
        } catch (error) {
            console.error(`${platform} OAuth error:`, error);
            throw error;
        }
    }

    // Exchange authorization code for access token
    private async exchangeCodeForToken(
        code: string,
        config: (typeof OAUTH_CONFIG)[keyof typeof OAUTH_CONFIG],
        platform: keyof typeof OAUTH_CONFIG
    ): Promise<AuthToken> {
        const body = new URLSearchParams({
            client_id: config.clientId,
            client_secret: `YOUR_${platform.toUpperCase()}_CLIENT_SECRET`, // Replace with actual secrets
            code,
            grant_type: "authorization_code",
            redirect_uri: this.redirectUri,
        });

        const response = await fetch(config.tokenUrl, {
            method: "POST",
            headers: {
                "Content-Type": "application/x-www-form-urlencoded",
                Accept: "application/json",
            },
            body: body.toString(),
        });

        if (!response.ok) {
            throw new Error(`Token exchange failed: ${response.statusText}`);
        }

        const data = await response.json();

        return {
            accessToken: data.access_token,
            refreshToken: data.refresh_token,
            expiresIn: data.expires_in,
            tokenType: data.token_type,
            scope: data.scope,
        };
    }

    // Store token securely
    private async storeToken(platform: string, token: AuthToken): Promise<void> {
        try {
            await SecureStore.setItemAsync(`${platform}_token`, JSON.stringify(token));
        } catch (error) {
            console.error(`Error storing ${platform} token:`, error);
            throw error;
        }
    }

    // Get stored token
    async getStoredToken(platform: string): Promise<AuthToken | null> {
        try {
            const tokenString = await SecureStore.getItemAsync(`${platform}_token`);
            return tokenString ? JSON.parse(tokenString) : null;
        } catch (error) {
            console.error(`Error retrieving ${platform} token:`, error);
            return null;
        }
    }

    // Remove stored token
    async removeToken(platform: string): Promise<void> {
        try {
            await SecureStore.deleteItemAsync(`${platform}_token`);
        } catch (error) {
            console.error(`Error removing ${platform} token:`, error);
        }
    }

    // Facebook Authentication
    async authenticateFacebook(): Promise<AuthToken> {
        return this.performOAuth("facebook", OAUTH_CONFIG.facebook);
    }

    // Instagram Authentication
    async authenticateInstagram(): Promise<AuthToken> {
        return this.performOAuth("instagram", OAUTH_CONFIG.instagram);
    }

    // Twitter Authentication
    async authenticateTwitter(): Promise<AuthToken> {
        return this.performOAuth("twitter", OAUTH_CONFIG.twitter);
    }

    // Check if user is authenticated for a platform
    async isAuthenticated(platform: string): Promise<boolean> {
        const token = await this.getStoredToken(platform);
        if (!token) return false;

        // Check if token is expired
        if (token.expiresIn) {
            // This is a simplified check - in production, you'd want to store the timestamp
            // when the token was obtained and compare with current time
            return true;
        }

        return true;
    }

    // Logout from a platform
    async logout(platform: string): Promise<void> {
        await this.removeToken(platform);
    }

    // Get user profile (to be implemented with actual API calls)
    async getUserProfile(platform: string): Promise<UserProfile | null> {
        const token = await this.getStoredToken(platform);
        if (!token) return null;

        // This would make actual API calls to get user profile
        // For now, return mock data
        return {
            id: `user_${platform}`,
            name: `User ${platform}`,
            username: `user_${platform}`,
            platform: platform as any,
        };
    }
}

export const authService = new AuthService();
