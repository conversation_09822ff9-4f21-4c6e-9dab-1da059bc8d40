import { authService } from "./authService";

export interface SocialPost {
    id: string;
    platform: "facebook" | "instagram" | "twitter";
    author: {
        name: string;
        username: string;
        avatar?: string;
    };
    content: string;
    timestamp: Date;
    likes?: number;
    comments?: number;
    shares?: number;
    image?: string;
    video?: string;
    url?: string;
}

export interface APIResponse<T> {
    data: T;
    success: boolean;
    error?: string;
    nextPageToken?: string;
}

class SocialMediaAPI {
    private baseUrls = {
        facebook: "https://graph.facebook.com/v18.0",
        instagram: "https://graph.instagram.com",
        twitter: "https://api.twitter.com/2",
    };

    // Generic API request method
    private async makeRequest<T>(
        platform: keyof typeof this.baseUrls,
        endpoint: string,
        options: RequestInit = {}
    ): Promise<APIResponse<T>> {
        try {
            const token = await authService.getStoredToken(platform);
            if (!token) {
                throw new Error(`No authentication token found for ${platform}`);
            }

            const url = `${this.baseUrls[platform]}${endpoint}`;
            const response = await fetch(url, {
                ...options,
                headers: {
                    Authorization: `Bearer ${token.accessToken}`,
                    "Content-Type": "application/json",
                    ...options.headers,
                },
            });

            if (!response.ok) {
                throw new Error(`API request failed: ${response.statusText}`);
            }

            const data = await response.json();
            return {
                data,
                success: true,
            };
        } catch (error) {
            console.error(`${platform} API error:`, error);
            return {
                data: null as T,
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
            };
        }
    }

    // Facebook API methods
    async getFacebookPosts(limit: number = 25): Promise<APIResponse<SocialPost[]>> {
        try {
            const response = await this.makeRequest<any>(
                "facebook",
                `/me/posts?fields=id,message,created_time,likes.summary(true),comments.summary(true),shares&limit=${limit}`
            );

            if (!response.success) {
                return response as APIResponse<SocialPost[]>;
            }

            const posts: SocialPost[] =
                response.data.data?.map((post: any) => ({
                    id: post.id,
                    platform: "facebook" as const,
                    author: {
                        name: "Facebook User", // Would get from /me endpoint
                        username: "facebook_user",
                    },
                    content: post.message || "",
                    timestamp: new Date(post.created_time),
                    likes: post.likes?.summary?.total_count || 0,
                    comments: post.comments?.summary?.total_count || 0,
                    shares: post.shares?.count || 0,
                })) || [];

            return {
                data: posts,
                success: true,
                nextPageToken: response.data.paging?.next,
            };
        } catch (error) {
            return {
                data: [],
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
            };
        }
    }

    // Instagram API methods
    async getInstagramPosts(limit: number = 25): Promise<APIResponse<SocialPost[]>> {
        try {
            const response = await this.makeRequest<any>(
                "instagram",
                `/me/media?fields=id,caption,media_type,media_url,thumbnail_url,timestamp,like_count,comments_count&limit=${limit}`
            );

            if (!response.success) {
                return response as APIResponse<SocialPost[]>;
            }

            const posts: SocialPost[] =
                response.data.data?.map((post: any) => ({
                    id: post.id,
                    platform: "instagram" as const,
                    author: {
                        name: "Instagram User", // Would get from user info
                        username: "instagram_user",
                    },
                    content: post.caption || "",
                    timestamp: new Date(post.timestamp),
                    likes: post.like_count || 0,
                    comments: post.comments_count || 0,
                    image: post.media_type === "IMAGE" ? post.media_url : post.thumbnail_url,
                    video: post.media_type === "VIDEO" ? post.media_url : undefined,
                })) || [];

            return {
                data: posts,
                success: true,
                nextPageToken: response.data.paging?.next,
            };
        } catch (error) {
            return {
                data: [],
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
            };
        }
    }

    // Twitter API methods
    async getTwitterPosts(limit: number = 25): Promise<APIResponse<SocialPost[]>> {
        try {
            const response = await this.makeRequest<any>(
                "twitter",
                `/users/me/tweets?tweet.fields=created_at,public_metrics,attachments&expansions=author_id&user.fields=name,username,profile_image_url&max_results=${limit}`
            );

            if (!response.success) {
                return response as APIResponse<SocialPost[]>;
            }

            const tweets = response.data.data || [];
            const users = response.data.includes?.users || [];
            const userMap = new Map(users.map((user: any) => [user.id, user]));

            const posts: SocialPost[] = tweets.map((tweet: any) => {
                const author = userMap.get(tweet.author_id) || {};
                return {
                    id: tweet.id,
                    platform: "twitter" as const,
                    author: {
                        name: author.name || "Twitter User",
                        username: author.username || "twitter_user",
                        avatar: author.profile_image_url,
                    },
                    content: tweet.text || "",
                    timestamp: new Date(tweet.created_at),
                    likes: tweet.public_metrics?.like_count || 0,
                    comments: tweet.public_metrics?.reply_count || 0,
                    shares: tweet.public_metrics?.retweet_count || 0,
                };
            });

            return {
                data: posts,
                success: true,
                nextPageToken: response.data.meta?.next_token,
            };
        } catch (error) {
            return {
                data: [],
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
            };
        }
    }

    // Get combined feed from all connected platforms
    async getCombinedFeed(limit: number = 50): Promise<APIResponse<SocialPost[]>> {
        try {
            const allPosts: SocialPost[] = [];
            const errors: string[] = [];

            // Check which platforms are authenticated
            const platforms = ["facebook", "instagram", "twitter"] as const;
            const authenticatedPlatforms = [];

            for (const platform of platforms) {
                const isAuth = await authService.isAuthenticated(platform);
                if (isAuth) {
                    authenticatedPlatforms.push(platform);
                }
            }

            // Fetch posts from each authenticated platform
            const fetchPromises = authenticatedPlatforms.map(async (platform) => {
                try {
                    let response: APIResponse<SocialPost[]>;

                    switch (platform) {
                        case "facebook":
                            response = await this.getFacebookPosts(
                                Math.ceil(limit / authenticatedPlatforms.length)
                            );
                            break;
                        case "instagram":
                            response = await this.getInstagramPosts(
                                Math.ceil(limit / authenticatedPlatforms.length)
                            );
                            break;
                        case "twitter":
                            response = await this.getTwitterPosts(
                                Math.ceil(limit / authenticatedPlatforms.length)
                            );
                            break;
                        default:
                            return;
                    }

                    if (response.success) {
                        allPosts.push(...response.data);
                    } else {
                        errors.push(`${platform}: ${response.error}`);
                    }
                } catch (error) {
                    errors.push(
                        `${platform}: ${error instanceof Error ? error.message : "Unknown error"}`
                    );
                }
            });

            await Promise.all(fetchPromises);

            // Sort posts by timestamp (newest first)
            allPosts.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

            // Limit the results
            const limitedPosts = allPosts.slice(0, limit);

            return {
                data: limitedPosts,
                success: true,
                error: errors.length > 0 ? errors.join("; ") : undefined,
            };
        } catch (error) {
            return {
                data: [],
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
            };
        }
    }

    // Refresh posts for a specific platform
    async refreshPlatformPosts(
        platform: "facebook" | "instagram" | "twitter"
    ): Promise<APIResponse<SocialPost[]>> {
        switch (platform) {
            case "facebook":
                return this.getFacebookPosts();
            case "instagram":
                return this.getInstagramPosts();
            case "twitter":
                return this.getTwitterPosts();
            default:
                return {
                    data: [],
                    success: false,
                    error: "Invalid platform",
                };
        }
    }
}

export const socialMediaAPI = new SocialMediaAPI();
