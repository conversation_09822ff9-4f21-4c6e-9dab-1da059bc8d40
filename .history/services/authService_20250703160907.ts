import AsyncStorage from "@react-native-async-storage/async-storage";
import * as AuthSession from "expo-auth-session";
import * as SecureStore from "expo-secure-store";
import { Platform } from "react-native";

export interface InstagramCredentials {
    username: string;
    password: string;
}

// Simple Instagram scraper stub for web compatibility
const instagramScraper = {
    login: async (credentials: InstagramCredentials): Promise<boolean> => {
        if (Platform.OS === "web") {
            // Web demo mode
            return true;
        }
        // For mobile, would use actual scraper
        return false;
    },
    isAuthenticated: (): boolean => {
        return Platform.OS === "web"; // Always true for web demo
    },
    getProfile: async (): Promise<any> => {
        if (Platform.OS === "web") {
            return {
                username: "demo_user",
                fullName: "Demo User",
                profilePicUrl: "",
            };
        }
        return null;
    },
    getFeedPosts: async (limit: number): Promise<any[]> => {
        return []; // Empty for now
    },
};

// OAuth Configuration - will be set dynamically
let OAUTH_CONFIG = {
    facebook: {
        clientId: "YOUR_FACEBOOK_APP_ID", // Replace with your Facebook App ID
        scopes: ["public_profile", "email", "user_posts"],
        authUrl: "https://www.facebook.com/v18.0/dialog/oauth",
        tokenUrl: "https://graph.facebook.com/v18.0/oauth/access_token",
    },
    instagram: {
        clientId: "", // Will be set from user input
        scopes: ["user_profile", "user_media"],
        authUrl: "https://api.instagram.com/oauth/authorize",
        tokenUrl: "https://api.instagram.com/oauth/access_token",
    },
    twitter: {
        clientId: "YOUR_TWITTER_CLIENT_ID", // Replace with your Twitter Client ID
        scopes: ["tweet.read", "users.read", "offline.access"],
        authUrl: "https://twitter.com/i/oauth2/authorize",
        tokenUrl: "https://api.twitter.com/2/oauth2/token",
    },
};

export interface AuthToken {
    accessToken: string;
    refreshToken?: string;
    expiresIn?: number;
    tokenType?: string;
    scope?: string;
}

export interface UserProfile {
    id: string;
    name: string;
    username: string;
    email?: string;
    avatar?: string;
    platform: "facebook" | "instagram" | "twitter";
}

class AuthService {
    private redirectUri: string;

    constructor() {
        this.redirectUri = AuthSession.makeRedirectUri({
            scheme: "myfirstexpoapp",
            path: "auth",
        });
    }

    // Update OAuth configuration for a platform
    updatePlatformConfig(
        platform: keyof typeof OAUTH_CONFIG,
        config: Partial<(typeof OAUTH_CONFIG)[keyof typeof OAUTH_CONFIG]>
    ) {
        OAUTH_CONFIG[platform] = { ...OAUTH_CONFIG[platform], ...config };
    }

    // Generic OAuth flow
    private async performOAuth(
        platform: keyof typeof OAUTH_CONFIG,
        config: (typeof OAUTH_CONFIG)[keyof typeof OAUTH_CONFIG]
    ): Promise<AuthToken> {
        try {
            const request = new AuthSession.AuthRequest({
                clientId: config.clientId,
                scopes: config.scopes,
                redirectUri: this.redirectUri,
                responseType: AuthSession.ResponseType.Code,
                extraParams: {},
            });

            const result = await request.promptAsync({
                authorizationEndpoint: config.authUrl,
            });

            if (result.type === "success") {
                // Exchange code for token
                const tokenResponse = await this.exchangeCodeForToken(
                    result.params.code,
                    config,
                    platform
                );

                // Store token securely
                await this.storeToken(platform, tokenResponse);

                return tokenResponse;
            } else {
                throw new Error(`OAuth failed: ${result.type}`);
            }
        } catch (error) {
            console.error(`${platform} OAuth error:`, error);
            throw error;
        }
    }

    // Exchange authorization code for access token
    private async exchangeCodeForToken(
        code: string,
        config: (typeof OAUTH_CONFIG)[keyof typeof OAUTH_CONFIG],
        platform: keyof typeof OAUTH_CONFIG
    ): Promise<AuthToken> {
        const body = new URLSearchParams({
            client_id: config.clientId,
            client_secret: `YOUR_${platform.toUpperCase()}_CLIENT_SECRET`, // Replace with actual secrets
            code,
            grant_type: "authorization_code",
            redirect_uri: this.redirectUri,
        });

        const response = await fetch(config.tokenUrl, {
            method: "POST",
            headers: {
                "Content-Type": "application/x-www-form-urlencoded",
                Accept: "application/json",
            },
            body: body.toString(),
        });

        if (!response.ok) {
            throw new Error(`Token exchange failed: ${response.statusText}`);
        }

        const data = await response.json();

        return {
            accessToken: data.access_token,
            refreshToken: data.refresh_token,
            expiresIn: data.expires_in,
            tokenType: data.token_type,
            scope: data.scope,
        };
    }

    // Store token securely
    private async storeToken(platform: string, token: AuthToken): Promise<void> {
        try {
            const tokenString = JSON.stringify(token);
            if (Platform.OS === "web") {
                // Check if we're in browser environment
                if (typeof window !== "undefined") {
                    // Use AsyncStorage for web
                    await AsyncStorage.setItem(`${platform}_token`, tokenString);
                }
                // If SSR, just skip storage
            } else {
                // Use SecureStore for mobile
                await SecureStore.setItemAsync(`${platform}_token`, tokenString);
            }
        } catch (error) {
            console.error(`Error storing ${platform} token:`, error);
            throw error;
        }
    }

    // Get stored token
    async getStoredToken(platform: string): Promise<AuthToken | null> {
        try {
            let tokenString: string | null = null;

            if (Platform.OS === "web") {
                // Check if we're in browser environment
                if (typeof window !== "undefined") {
                    // Use AsyncStorage for web
                    tokenString = await AsyncStorage.getItem(`${platform}_token`);
                } else {
                    // SSR environment, return null
                    return null;
                }
            } else {
                // Use SecureStore for mobile
                tokenString = await SecureStore.getItemAsync(`${platform}_token`);
            }

            return tokenString ? JSON.parse(tokenString) : null;
        } catch (error) {
            console.error(`Error retrieving ${platform} token:`, error);
            return null;
        }
    }

    // Remove stored token
    async removeToken(platform: string): Promise<void> {
        try {
            if (Platform.OS === "web") {
                // Check if we're in browser environment
                if (typeof window !== "undefined") {
                    // Use AsyncStorage for web
                    await AsyncStorage.removeItem(`${platform}_token`);
                }
                // If SSR, just skip removal
            } else {
                // Use SecureStore for mobile
                await SecureStore.deleteItemAsync(`${platform}_token`);
            }
        } catch (error) {
            console.error(`Error removing ${platform} token:`, error);
        }
    }

    // Facebook Authentication
    async authenticateFacebook(): Promise<AuthToken> {
        return this.performOAuth("facebook", OAUTH_CONFIG.facebook);
    }

    // Instagram Authentication (OAuth)
    async authenticateInstagram(): Promise<AuthToken> {
        return this.performOAuth("instagram", OAUTH_CONFIG.instagram);
    }

    // Instagram Authentication with Username/Password (Scraping)
    async authenticateInstagramWithCredentials(
        credentials: InstagramCredentials
    ): Promise<AuthToken> {
        try {
            // For web, simulate login (since scraping doesn't work in browser)
            if (Platform.OS === "web") {
                // Simulate a delay
                await new Promise((resolve) => setTimeout(resolve, 1000));

                // Create a mock token for demo purposes
                const token: AuthToken = {
                    accessToken: `demo_session_${Date.now()}`,
                    tokenType: "demo",
                    expiresIn: 3600, // 1 hour
                };

                // Store the token
                await this.storeToken("instagram", token);
                return token;
            }

            // For mobile, use actual scraper
            const success = await instagramScraper.login(credentials);

            if (!success) {
                throw new Error("Invalid username or password");
            }

            // Create a mock token for scraping session
            const token: AuthToken = {
                accessToken: `scraper_session_${Date.now()}`,
                tokenType: "scraper",
                expiresIn: 3600, // 1 hour
            };

            // Store the token
            await this.storeToken("instagram", token);

            return token;
        } catch (error) {
            console.error("Instagram scraper authentication error:", error);
            throw error;
        }
    }

    // Twitter Authentication
    async authenticateTwitter(): Promise<AuthToken> {
        return this.performOAuth("twitter", OAUTH_CONFIG.twitter);
    }

    // Check if user is authenticated for a platform
    async isAuthenticated(platform: string): Promise<boolean> {
        const token = await this.getStoredToken(platform);
        if (!token) return false;

        // Check if token is expired
        if (token.expiresIn) {
            // This is a simplified check - in production, you'd want to store the timestamp
            // when the token was obtained and compare with current time
            return true;
        }

        return true;
    }

    // Logout from a platform
    async logout(platform: string): Promise<void> {
        await this.removeToken(platform);
    }

    // Get user profile
    async getUserProfile(platform: string): Promise<UserProfile | null> {
        const token = await this.getStoredToken(platform);
        if (!token) return null;

        try {
            if (
                platform === "instagram" &&
                (token.tokenType === "scraper" || token.tokenType === "demo")
            ) {
                // For web demo or mobile scraper
                if (
                    token.tokenType === "demo" ||
                    (token.tokenType === "scraper" && instagramScraper.isAuthenticated())
                ) {
                    if (token.tokenType === "demo") {
                        // Return demo profile for web
                        return {
                            id: "demo_user",
                            name: "Demo Instagram User",
                            username: "demo_user",
                            email: undefined,
                            avatar: undefined,
                            platform: "instagram",
                        };
                    } else {
                        // Use Instagram scraper to get profile for mobile
                        const profile = await instagramScraper.getProfile();
                        if (profile) {
                            return {
                                id: profile.username,
                                name: profile.fullName || profile.username,
                                username: profile.username,
                                email: undefined,
                                avatar: profile.profilePicUrl,
                                platform: "instagram",
                            };
                        }
                    }
                }
            }

            // For other platforms or fallback, return mock data
            return {
                id: `user_${platform}`,
                name: `User ${platform}`,
                username: `user_${platform}`,
                platform: platform as any,
            };
        } catch (error) {
            console.error(`Error getting ${platform} profile:`, error);
            return null;
        }
    }
}

export const authService = new AuthService();
