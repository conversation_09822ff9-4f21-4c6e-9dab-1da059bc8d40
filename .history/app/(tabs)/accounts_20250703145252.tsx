import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Colors } from '@/constants/Colors';
import { Typography, Spacing, BorderRadius } from '@/constants/Typography';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';

interface SocialAccount {
  id: string;
  platform: 'facebook' | 'instagram' | 'twitter';
  username: string;
  isConnected: boolean;
  avatar?: string;
}

const SOCIAL_PLATFORMS = [
  {
    id: 'facebook',
    name: 'Facebook',
    icon: 'f.square.fill',
    color: '#1877f2',
    description: 'Connect your Facebook account to see posts and updates',
  },
  {
    id: 'instagram',
    name: 'Instagram',
    icon: 'camera.fill',
    color: '#e4405f',
    description: 'Connect your Instagram account to see photos and stories',
  },
  {
    id: 'twitter',
    name: '<PERSON> (Twitter)',
    icon: 'bird.fill',
    color: '#1da1f2',
    description: 'Connect your X account to see tweets and trends',
  },
] as const;

export default function AccountsScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  const [accounts, setAccounts] = useState<SocialAccount[]>([
    {
      id: '1',
      platform: 'facebook',
      username: '',
      isConnected: false,
    },
    {
      id: '2',
      platform: 'instagram',
      username: '',
      isConnected: false,
    },
    {
      id: '3',
      platform: 'twitter',
      username: '',
      isConnected: false,
    },
  ]);

  const handleConnectAccount = (platformId: string) => {
    // TODO: Implement OAuth authentication
    Alert.alert(
      'Connect Account',
      `Connect your ${platformId} account?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Connect',
          onPress: () => {
            // Simulate connection for now
            setAccounts(prev =>
              prev.map(account =>
                account.platform === platformId
                  ? { ...account, isConnected: true, username: `user_${platformId}` }
                  : account
              )
            );
          },
        },
      ]
    );
  };

  const handleDisconnectAccount = (platformId: string) => {
    Alert.alert(
      'Disconnect Account',
      `Are you sure you want to disconnect your ${platformId} account?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Disconnect',
          style: 'destructive',
          onPress: () => {
            setAccounts(prev =>
              prev.map(account =>
                account.platform === platformId
                  ? { ...account, isConnected: false, username: '' }
                  : account
              )
            );
          },
        },
      ]
    );
  };

  const renderPlatformCard = (platform: typeof SOCIAL_PLATFORMS[0]) => {
    const account = accounts.find(acc => acc.platform === platform.id);
    const isConnected = account?.isConnected || false;

    return (
      <Card key={platform.id} style={styles.platformCard}>
        <View style={styles.platformHeader}>
          <View style={styles.platformInfo}>
            <View style={[styles.iconContainer, { backgroundColor: platform.color }]}>
              <IconSymbol name={platform.icon} size={24} color="#ffffff" />
            </View>
            <View style={styles.platformDetails}>
              <Text style={[styles.platformName, { color: colors.text }]}>
                {platform.name}
              </Text>
              <Text style={[styles.platformDescription, { color: colors.textSecondary }]}>
                {isConnected ? `Connected as @${account?.username}` : platform.description}
              </Text>
            </View>
          </View>
          <View style={styles.connectionStatus}>
            {isConnected ? (
              <View style={[styles.statusBadge, { backgroundColor: colors.success }]}>
                <Text style={styles.statusText}>Connected</Text>
              </View>
            ) : (
              <View style={[styles.statusBadge, { backgroundColor: colors.textMuted }]}>
                <Text style={styles.statusText}>Not Connected</Text>
              </View>
            )}
          </View>
        </View>
        
        <View style={styles.platformActions}>
          {isConnected ? (
            <Button
              title="Disconnect"
              variant="outline"
              size="sm"
              onPress={() => handleDisconnectAccount(platform.id)}
              style={{ borderColor: colors.error }}
              textStyle={{ color: colors.error }}
            />
          ) : (
            <Button
              title="Connect"
              variant="primary"
              size="sm"
              onPress={() => handleConnectAccount(platform.id)}
            />
          )}
        </View>
      </Card>
    );
  };

  const connectedCount = accounts.filter(acc => acc.isConnected).length;

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>
          Social Accounts
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Connect your social media accounts to see all your feeds in one place
        </Text>
      </View>

      <Card style={styles.summaryCard}>
        <View style={styles.summaryContent}>
          <Text style={[styles.summaryTitle, { color: colors.text }]}>
            Connected Accounts
          </Text>
          <Text style={[styles.summaryCount, { color: colors.primary }]}>
            {connectedCount} of {accounts.length}
          </Text>
        </View>
      </Card>

      <View style={styles.platformsList}>
        {SOCIAL_PLATFORMS.map(renderPlatformCard)}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: Spacing.lg,
    paddingBottom: Spacing.md,
  },
  title: {
    fontSize: Typography.sizes['3xl'],
    fontWeight: Typography.weights.bold,
    marginBottom: Spacing.sm,
  },
  subtitle: {
    fontSize: Typography.sizes.base,
    lineHeight: Typography.sizes.base * Typography.lineHeights.relaxed,
  },
  summaryCard: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  summaryContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  summaryTitle: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.semiBold,
  },
  summaryCount: {
    fontSize: Typography.sizes['2xl'],
    fontWeight: Typography.weights.bold,
  },
  platformsList: {
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing['6xl'],
  },
  platformCard: {
    marginBottom: Spacing.lg,
  },
  platformHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.lg,
  },
  platformInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: BorderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  platformDetails: {
    flex: 1,
  },
  platformName: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.semiBold,
    marginBottom: Spacing.xs,
  },
  platformDescription: {
    fontSize: Typography.sizes.sm,
    lineHeight: Typography.sizes.sm * Typography.lineHeights.normal,
  },
  connectionStatus: {
    marginLeft: Spacing.md,
  },
  statusBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.md,
  },
  statusText: {
    color: '#ffffff',
    fontSize: Typography.sizes.xs,
    fontWeight: Typography.weights.medium,
  },
  platformActions: {
    alignItems: 'flex-start',
  },
});
