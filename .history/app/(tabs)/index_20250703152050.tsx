import { Card } from "@/components/ui/Card";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { Colors } from "@/constants/Colors";
import { BorderRadius, Spacing, Typography } from "@/constants/Typography";
import { useColorScheme } from "@/hooks/useColorScheme";
import { accountStore } from "@/stores/AccountStore";
import { feedStore } from "@/stores/FeedStore";
import { observer } from "mobx-react-lite";
import React, { useEffect } from "react";
import { RefreshControl, ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";

interface SocialPost {
    id: string;
    platform: "facebook" | "instagram" | "twitter";
    author: {
        name: string;
        username: string;
        avatar?: string;
    };
    content: string;
    timestamp: Date;
    likes?: number;
    comments?: number;
    shares?: number;
    image?: string;
}

// Mock data for demonstration
const MOCK_POSTS: SocialPost[] = [
    {
        id: "1",
        platform: "twitter",
        author: {
            name: "Tech News",
            username: "technews",
        },
        content:
            "Breaking: New React Native features announced at the conference! 🚀 #ReactNative #Mobile",
        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
        likes: 124,
        comments: 23,
        shares: 45,
    },
    {
        id: "2",
        platform: "instagram",
        author: {
            name: "Design Studio",
            username: "designstudio",
        },
        content: "Beautiful sunset from our office window today 🌅 #design #inspiration #sunset",
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
        likes: 89,
        comments: 12,
    },
    {
        id: "3",
        platform: "facebook",
        author: {
            name: "John Doe",
            username: "johndoe",
        },
        content:
            "Just finished reading an amazing book about mobile development. Highly recommend it to anyone interested in the field!",
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
        likes: 56,
        comments: 8,
        shares: 12,
    },
];

const FeedScreen = observer(() => {
    const colorScheme = useColorScheme();
    const colors = Colors[colorScheme ?? "light"];

    useEffect(() => {
        // Load feed when component mounts
        if (accountStore.connectionCount > 0) {
            feedStore.loadFeed();
        }
    }, [accountStore.connectionCount]);

    const onRefresh = async () => {
        await feedStore.refreshFeed();
    };

    const formatTimestamp = (timestamp: Date) => {
        const now = new Date();
        const diff = now.getTime() - timestamp.getTime();
        const minutes = Math.floor(diff / (1000 * 60));
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));

        if (minutes < 60) {
            return `${minutes}m`;
        } else if (hours < 24) {
            return `${hours}h`;
        } else {
            return `${days}d`;
        }
    };

    const getPlatformIcon = (platform: string) => {
        switch (platform) {
            case "facebook":
                return "f.square.fill";
            case "instagram":
                return "camera.fill";
            case "twitter":
                return "bird.fill";
            default:
                return "questionmark.circle";
        }
    };

    const getPlatformColor = (platform: string) => {
        switch (platform) {
            case "facebook":
                return colors.facebook;
            case "instagram":
                return colors.instagram;
            case "twitter":
                return colors.twitter;
            default:
                return colors.textMuted;
        }
    };

    const renderPost = (post: SocialPost) => (
        <Card key={post.id} style={styles.postCard}>
            <View style={styles.postHeader}>
                <View style={styles.authorInfo}>
                    <View
                        style={[
                            styles.avatarContainer,
                            { backgroundColor: colors.backgroundSecondary },
                        ]}
                    >
                        <Text style={[styles.avatarText, { color: colors.textSecondary }]}>
                            {post.author.name.charAt(0).toUpperCase()}
                        </Text>
                    </View>
                    <View style={styles.authorDetails}>
                        <Text style={[styles.authorName, { color: colors.text }]}>
                            {post.author.name}
                        </Text>
                        <View style={styles.postMeta}>
                            <Text style={[styles.username, { color: colors.textSecondary }]}>
                                @{post.author.username}
                            </Text>
                            <Text style={[styles.separator, { color: colors.textMuted }]}>•</Text>
                            <Text style={[styles.timestamp, { color: colors.textSecondary }]}>
                                {formatTimestamp(post.timestamp)}
                            </Text>
                        </View>
                    </View>
                </View>
                <View
                    style={[
                        styles.platformBadge,
                        { backgroundColor: getPlatformColor(post.platform) },
                    ]}
                >
                    <IconSymbol name={getPlatformIcon(post.platform)} size={16} color="#ffffff" />
                </View>
            </View>

            <Text style={[styles.postContent, { color: colors.text }]}>{post.content}</Text>

            <View style={styles.postActions}>
                {post.likes !== undefined && (
                    <TouchableOpacity style={styles.actionButton}>
                        <IconSymbol name="heart" size={18} color={colors.textMuted} />
                        <Text style={[styles.actionText, { color: colors.textMuted }]}>
                            {post.likes}
                        </Text>
                    </TouchableOpacity>
                )}
                {post.comments !== undefined && (
                    <TouchableOpacity style={styles.actionButton}>
                        <IconSymbol name="bubble.left" size={18} color={colors.textMuted} />
                        <Text style={[styles.actionText, { color: colors.textMuted }]}>
                            {post.comments}
                        </Text>
                    </TouchableOpacity>
                )}
                {post.shares !== undefined && (
                    <TouchableOpacity style={styles.actionButton}>
                        <IconSymbol
                            name="arrowshape.turn.up.right"
                            size={18}
                            color={colors.textMuted}
                        />
                        <Text style={[styles.actionText, { color: colors.textMuted }]}>
                            {post.shares}
                        </Text>
                    </TouchableOpacity>
                )}
            </View>
        </Card>
    );

    return (
        <View style={[styles.container, { backgroundColor: colors.background }]}>
            <View
                style={[
                    styles.header,
                    { backgroundColor: colors.background, borderBottomColor: colors.border },
                ]}
            >
                <Text style={[styles.title, { color: colors.text }]}>Social Feed</Text>
            </View>

            <ScrollView
                style={styles.feed}
                refreshControl={
                    <RefreshControl refreshing={feedStore.isRefreshing} onRefresh={onRefresh} />
                }
                showsVerticalScrollIndicator={false}
            >
                {feedStore.isEmpty ? (
                    <View style={styles.emptyState}>
                        <IconSymbol name="wifi.slash" size={48} color={colors.textMuted} />
                        <Text style={[styles.emptyTitle, { color: colors.text }]}>
                            No posts yet
                        </Text>
                        <Text style={[styles.emptyDescription, { color: colors.textSecondary }]}>
                            Connect your social accounts to see your feed here
                        </Text>
                    </View>
                ) : (
                    <View style={styles.postsContainer}>{feedStore.posts.map(renderPost)}</View>
                )}
            </ScrollView>
        </View>
    );
});

export default FeedScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        paddingHorizontal: Spacing.lg,
        paddingVertical: Spacing.md,
        borderBottomWidth: 1,
    },
    title: {
        fontSize: Typography.sizes["2xl"],
        fontWeight: Typography.weights.bold,
    },
    feed: {
        flex: 1,
    },
    postsContainer: {
        padding: Spacing.lg,
    },
    postCard: {
        marginBottom: Spacing.lg,
    },
    postHeader: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "flex-start",
        marginBottom: Spacing.md,
    },
    authorInfo: {
        flexDirection: "row",
        flex: 1,
    },
    avatarContainer: {
        width: 40,
        height: 40,
        borderRadius: BorderRadius.full,
        justifyContent: "center",
        alignItems: "center",
        marginRight: Spacing.md,
    },
    avatarText: {
        fontSize: Typography.sizes.lg,
        fontWeight: Typography.weights.semiBold,
    },
    authorDetails: {
        flex: 1,
    },
    authorName: {
        fontSize: Typography.sizes.base,
        fontWeight: Typography.weights.semiBold,
        marginBottom: Spacing.xs,
    },
    postMeta: {
        flexDirection: "row",
        alignItems: "center",
    },
    username: {
        fontSize: Typography.sizes.sm,
    },
    separator: {
        marginHorizontal: Spacing.sm,
        fontSize: Typography.sizes.sm,
    },
    timestamp: {
        fontSize: Typography.sizes.sm,
    },
    platformBadge: {
        width: 28,
        height: 28,
        borderRadius: BorderRadius.md,
        justifyContent: "center",
        alignItems: "center",
    },
    postContent: {
        fontSize: Typography.sizes.base,
        lineHeight: Typography.sizes.base * Typography.lineHeights.relaxed,
        marginBottom: Spacing.lg,
    },
    postActions: {
        flexDirection: "row",
        alignItems: "center",
    },
    actionButton: {
        flexDirection: "row",
        alignItems: "center",
        marginRight: Spacing.xl,
    },
    actionText: {
        fontSize: Typography.sizes.sm,
        marginLeft: Spacing.sm,
    },
    emptyState: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        paddingVertical: Spacing["6xl"],
    },
    emptyTitle: {
        fontSize: Typography.sizes.xl,
        fontWeight: Typography.weights.semiBold,
        marginTop: Spacing.lg,
        marginBottom: Spacing.sm,
    },
    emptyDescription: {
        fontSize: Typography.sizes.base,
        textAlign: "center",
        paddingHorizontal: Spacing.xl,
    },
});
