import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { observer } from 'mobx-react-lite';
import { Colors } from '@/constants/Colors';
import { Typography, Spacing, BorderRadius } from '@/constants/Typography';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { accountStore } from '@/stores/AccountStore';
import { InstagramConfigModal, InstagramConfig } from '@/components/InstagramConfigModal';

const SOCIAL_PLATFORMS = [
  {
    id: 'facebook',
    name: 'Facebook',
    icon: 'f.square.fill',
    color: '#1877f2',
    description: 'Connect your Facebook account to see posts and updates',
  },
  {
    id: 'instagram',
    name: 'Instagram',
    icon: 'camera.fill',
    color: '#e4405f',
    description: 'Connect your Instagram account to see photos and stories',
  },
  {
    id: 'twitter',
    name: '<PERSON> (Twitter)',
    icon: 'bird.fill',
    color: '#1da1f2',
    description: 'Connect your X account to see tweets and trends',
  },
] as const;

const AccountsScreen = observer(() => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [showInstagramConfig, setShowInstagramConfig] = useState(false);

  useEffect(() => {
    // Load accounts when component mounts
    accountStore.loadStoredAccounts();
  }, []);

  const handleConnectAccount = async (platformId: string) => {
    try {
      if (platformId === 'instagram') {
        // Check if Instagram is configured
        const config = accountStore.getPlatformConfig('instagram');
        if (!config) {
          setShowInstagramConfig(true);
          return;
        }
      }

      await accountStore.connectAccount(platformId as any);
      Alert.alert('Success', `${platformId} account connected successfully!`);
    } catch (error) {
      Alert.alert(
        'Connection Failed',
        error instanceof Error ? error.message : 'Failed to connect account'
      );
    }
  };

  const handleDisconnectAccount = async (platformId: string) => {
    Alert.alert(
      'Disconnect Account',
      `Are you sure you want to disconnect your ${platformId} account?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Disconnect',
          style: 'destructive',
          onPress: async () => {
            try {
              await accountStore.disconnectAccount(platformId);
              Alert.alert('Success', `${platformId} account disconnected`);
            } catch (error) {
              Alert.alert('Error', 'Failed to disconnect account');
            }
          },
        },
      ]
    );
  };

  const handleInstagramConfig = (config: InstagramConfig) => {
    // Save Instagram configuration
    accountStore.setPlatformConfig({
      platform: 'instagram',
      clientId: config.clientId,
      clientSecret: config.clientSecret,
      redirectUri: config.redirectUri,
      scopes: ['user_profile', 'user_media'],
    });

    // Now try to connect
    handleConnectAccount('instagram');
  };

  const renderPlatformCard = (platform: any) => {
    const account = accountStore.getAccount(platform.id);
    const isConnected = accountStore.isConnected(platform.id);

    return (
      <Card key={platform.id} style={styles.platformCard}>
        <View style={styles.platformHeader}>
          <View style={styles.platformInfo}>
            <View style={[styles.iconContainer, { backgroundColor: platform.color }]}>
              <IconSymbol name={platform.icon} size={24} color="#ffffff" />
            </View>
            <View style={styles.platformDetails}>
              <Text style={[styles.platformName, { color: colors.text }]}>
                {platform.name}
              </Text>
              <Text style={[styles.platformDescription, { color: colors.textSecondary }]}>
                {isConnected && account 
                  ? `Connected as ${account.displayName || account.username}` 
                  : platform.description}
              </Text>
            </View>
          </View>
          <View style={styles.connectionStatus}>
            {isConnected ? (
              <View style={[styles.statusBadge, { backgroundColor: colors.success }]}>
                <Text style={styles.statusText}>Connected</Text>
              </View>
            ) : (
              <View style={[styles.statusBadge, { backgroundColor: colors.textMuted }]}>
                <Text style={styles.statusText}>Not Connected</Text>
              </View>
            )}
          </View>
        </View>
        
        <View style={styles.platformActions}>
          {isConnected ? (
            <Button
              title="Disconnect"
              variant="outline"
              size="sm"
              onPress={() => handleDisconnectAccount(platform.id)}
              style={{ borderColor: colors.error }}
              textStyle={{ color: colors.error }}
            />
          ) : (
            <Button
              title="Connect"
              variant="primary"
              size="sm"
              onPress={() => handleConnectAccount(platform.id)}
              loading={accountStore.isLoading}
            />
          )}
        </View>
      </Card>
    );
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>
          Social Accounts
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Connect your social media accounts to see all your feeds in one place
        </Text>
      </View>

      <Card style={styles.summaryCard}>
        <View style={styles.summaryContent}>
          <Text style={[styles.summaryTitle, { color: colors.text }]}>
            Connected Accounts
          </Text>
          <Text style={[styles.summaryCount, { color: colors.primary }]}>
            {accountStore.connectionCount} of {SOCIAL_PLATFORMS.length}
          </Text>
        </View>
      </Card>

      {accountStore.error && (
        <Card style={[styles.errorCard, { backgroundColor: colors.error + '10' }]}>
          <View style={styles.errorContent}>
            <IconSymbol name="exclamationmark.triangle.fill" size={20} color={colors.error} />
            <Text style={[styles.errorText, { color: colors.error }]}>
              {accountStore.error}
            </Text>
          </View>
          <Button
            title="Dismiss"
            variant="ghost"
            size="sm"
            onPress={() => accountStore.clearError()}
            textStyle={{ color: colors.error }}
          />
        </Card>
      )}

      <View style={styles.platformsList}>
        {SOCIAL_PLATFORMS.map(renderPlatformCard)}
      </View>

      <InstagramConfigModal
        visible={showInstagramConfig}
        onClose={() => setShowInstagramConfig(false)}
        onSave={handleInstagramConfig}
      />
    </ScrollView>
  );
});

export default AccountsScreen;
