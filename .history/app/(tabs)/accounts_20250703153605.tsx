import { InstagramConfig, InstagramConfigModal } from "@/components/InstagramConfigModal";
import { Button } from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { Colors } from "@/constants/Colors";
import { BorderRadius, Spacing, Typography } from "@/constants/Typography";
import { useColorScheme } from "@/hooks/useColorScheme";
import { accountStore } from "@/stores/AccountStore";
import { observer } from "mobx-react-lite";
import React, { useEffect, useState } from "react";
import { Alert, ScrollView, StyleSheet, Text, View } from "react-native";

const SOCIAL_PLATFORMS = [
    {
        id: "facebook",
        name: "Facebook",
        icon: "f.square.fill",
        color: "#1877f2",
        description: "Connect your Facebook account to see posts and updates",
    },
    {
        id: "instagram",
        name: "Instagram",
        icon: "camera.fill",
        color: "#e4405f",
        description: "Connect your Instagram account to see photos and stories",
    },
    {
        id: "twitter",
        name: "<PERSON> (Twitter)",
        icon: "bird.fill",
        color: "#1da1f2",
        description: "Connect your X account to see tweets and trends",
    },
] as const;

const AccountsScreen = observer(() => {
    const colorScheme = useColorScheme();
    const colors = Colors[colorScheme ?? "light"];
    const [showInstagramConfig, setShowInstagramConfig] = useState(false);
    const [showInstagramLogin, setShowInstagramLogin] = useState(false);

    useEffect(() => {
        // Load accounts when component mounts
        accountStore.loadStoredAccounts();
    }, []);

    const handleConnectAccount = async (platformId: string) => {
        try {
            if (platformId === "instagram") {
                // For Instagram, show login modal directly
                setShowInstagramLogin(true);
                return;
            }

            await accountStore.connectAccount(platformId as any);
            Alert.alert("Success", `${platformId} account connected successfully!`);
        } catch (error) {
            Alert.alert(
                "Connection Failed",
                error instanceof Error ? error.message : "Failed to connect account"
            );
        }
    };

    const handleDisconnectAccount = async (platformId: string) => {
        Alert.alert(
            "Disconnect Account",
            `Are you sure you want to disconnect your ${platformId} account?`,
            [
                { text: "Cancel", style: "cancel" },
                {
                    text: "Disconnect",
                    style: "destructive",
                    onPress: async () => {
                        try {
                            await accountStore.disconnectAccount(platformId);
                            Alert.alert("Success", `${platformId} account disconnected`);
                        } catch (error) {
                            Alert.alert("Error", "Failed to disconnect account");
                        }
                    },
                },
            ]
        );
    };

    const handleInstagramConfig = (config: InstagramConfig) => {
        // Save Instagram configuration
        accountStore.setPlatformConfig({
            platform: "instagram",
            clientId: config.clientId,
            clientSecret: config.clientSecret,
            redirectUri: config.redirectUri,
            scopes: ["user_profile", "user_media"],
        });

        // Now try to connect
        handleConnectAccount("instagram");
    };

    const renderPlatformCard = (platform: any) => {
        const account = accountStore.getAccount(platform.id);
        const isConnected = accountStore.isConnected(platform.id);

        return (
            <Card key={platform.id} style={styles.platformCard}>
                <View style={styles.platformHeader}>
                    <View style={styles.platformInfo}>
                        <View style={[styles.iconContainer, { backgroundColor: platform.color }]}>
                            <IconSymbol name={platform.icon} size={24} color="#ffffff" />
                        </View>
                        <View style={styles.platformDetails}>
                            <Text style={[styles.platformName, { color: colors.text }]}>
                                {platform.name}
                            </Text>
                            <Text
                                style={[
                                    styles.platformDescription,
                                    { color: colors.textSecondary },
                                ]}
                            >
                                {isConnected && account
                                    ? `Connected as ${account.displayName || account.username}`
                                    : platform.description}
                            </Text>
                        </View>
                    </View>
                    <View style={styles.connectionStatus}>
                        {isConnected ? (
                            <View style={[styles.statusBadge, { backgroundColor: colors.success }]}>
                                <Text style={styles.statusText}>Connected</Text>
                            </View>
                        ) : (
                            <View
                                style={[styles.statusBadge, { backgroundColor: colors.textMuted }]}
                            >
                                <Text style={styles.statusText}>Not Connected</Text>
                            </View>
                        )}
                    </View>
                </View>

                <View style={styles.platformActions}>
                    {isConnected ? (
                        <Button
                            title="Disconnect"
                            variant="outline"
                            size="sm"
                            onPress={() => handleDisconnectAccount(platform.id)}
                            style={{ borderColor: colors.error }}
                            textStyle={{ color: colors.error }}
                        />
                    ) : (
                        <Button
                            title="Connect"
                            variant="primary"
                            size="sm"
                            onPress={() => handleConnectAccount(platform.id)}
                            loading={accountStore.isLoading}
                        />
                    )}
                </View>
            </Card>
        );
    };

    return (
        <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
            <View style={styles.header}>
                <Text style={[styles.title, { color: colors.text }]}>Social Accounts</Text>
                <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
                    Connect your social media accounts to see all your feeds in one place
                </Text>
            </View>

            <Card style={styles.summaryCard}>
                <View style={styles.summaryContent}>
                    <Text style={[styles.summaryTitle, { color: colors.text }]}>
                        Connected Accounts
                    </Text>
                    <Text style={[styles.summaryCount, { color: colors.primary }]}>
                        {accountStore.connectionCount} of {SOCIAL_PLATFORMS.length}
                    </Text>
                </View>
            </Card>

            {accountStore.error && (
                <Card style={[styles.errorCard, { backgroundColor: colors.error + "10" }]}>
                    <View style={styles.errorContent}>
                        <IconSymbol
                            name="exclamationmark.triangle.fill"
                            size={20}
                            color={colors.error}
                        />
                        <Text style={[styles.errorText, { color: colors.error }]}>
                            {accountStore.error}
                        </Text>
                    </View>
                    <Button
                        title="Dismiss"
                        variant="ghost"
                        size="sm"
                        onPress={() => accountStore.clearError()}
                        textStyle={{ color: colors.error }}
                    />
                </Card>
            )}

            <View style={styles.platformsList}>{SOCIAL_PLATFORMS.map(renderPlatformCard)}</View>

            <InstagramConfigModal
                visible={showInstagramConfig}
                onClose={() => setShowInstagramConfig(false)}
                onSave={handleInstagramConfig}
            />
        </ScrollView>
    );
});

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        padding: Spacing.lg,
        paddingBottom: Spacing.md,
    },
    title: {
        fontSize: Typography.sizes["3xl"],
        fontWeight: Typography.weights.bold,
        marginBottom: Spacing.sm,
    },
    subtitle: {
        fontSize: Typography.sizes.base,
        lineHeight: Typography.sizes.base * Typography.lineHeights.relaxed,
    },
    summaryCard: {
        marginHorizontal: Spacing.lg,
        marginBottom: Spacing.lg,
    },
    summaryContent: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
    },
    summaryTitle: {
        fontSize: Typography.sizes.lg,
        fontWeight: Typography.weights.semiBold,
    },
    summaryCount: {
        fontSize: Typography.sizes["2xl"],
        fontWeight: Typography.weights.bold,
    },
    errorCard: {
        marginHorizontal: Spacing.lg,
        marginBottom: Spacing.lg,
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
    },
    errorContent: {
        flexDirection: "row",
        alignItems: "center",
        flex: 1,
    },
    errorText: {
        fontSize: Typography.sizes.sm,
        marginLeft: Spacing.sm,
        flex: 1,
    },
    platformsList: {
        paddingHorizontal: Spacing.lg,
        paddingBottom: Spacing["6xl"],
    },
    platformCard: {
        marginBottom: Spacing.lg,
    },
    platformHeader: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "flex-start",
        marginBottom: Spacing.lg,
    },
    platformInfo: {
        flexDirection: "row",
        flex: 1,
    },
    iconContainer: {
        width: 48,
        height: 48,
        borderRadius: BorderRadius.lg,
        justifyContent: "center",
        alignItems: "center",
        marginRight: Spacing.md,
    },
    platformDetails: {
        flex: 1,
    },
    platformName: {
        fontSize: Typography.sizes.lg,
        fontWeight: Typography.weights.semiBold,
        marginBottom: Spacing.xs,
    },
    platformDescription: {
        fontSize: Typography.sizes.sm,
        lineHeight: Typography.sizes.sm * Typography.lineHeights.normal,
    },
    connectionStatus: {
        marginLeft: Spacing.md,
    },
    statusBadge: {
        paddingHorizontal: Spacing.sm,
        paddingVertical: Spacing.xs,
        borderRadius: BorderRadius.md,
    },
    statusText: {
        color: "#ffffff",
        fontSize: Typography.sizes.xs,
        fontWeight: Typography.weights.medium,
    },
    platformActions: {
        alignItems: "flex-start",
    },
});

export default AccountsScreen;
