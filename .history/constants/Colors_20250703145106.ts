/**
 * Modern color palette for social media aggregator app
 * Inspired by contemporary design systems with accessibility in mind
 */

const tintColorLight = "#6366f1"; // Indigo-500
const tintColorDark = "#a5b4fc"; // Indigo-300

export const Colors = {
    light: {
        // Base colors
        text: "#0f172a", // Slate-900
        textSecondary: "#475569", // Slate-600
        textMuted: "#94a3b8", // Slate-400
        background: "#ffffff",
        backgroundSecondary: "#f8fafc", // Slate-50
        backgroundTertiary: "#f1f5f9", // Slate-100

        // Brand colors
        tint: tintColorLight,
        primary: "#6366f1", // Indigo-500
        primaryLight: "#e0e7ff", // Indigo-100

        // Social platform colors
        facebook: "#1877f2",
        instagram: "#e4405f",
        twitter: "#1da1f2",

        // UI elements
        border: "#e2e8f0", // Slate-200
        borderLight: "#f1f5f9", // Slate-100
        card: "#ffffff",
        cardShadow: "rgba(0, 0, 0, 0.05)",

        // Status colors
        success: "#10b981", // Emerald-500
        warning: "#f59e0b", // Amber-500
        error: "#ef4444", // Red-500

        // Tab navigation
        icon: "#64748b", // Slate-500
        tabIconDefault: "#94a3b8", // Slate-400
        tabIconSelected: tintColorLight,
        tabBackground: "rgba(255, 255, 255, 0.95)",
    },
    dark: {
        // Base colors
        text: "#f8fafc", // Slate-50
        textSecondary: "#cbd5e1", // Slate-300
        textMuted: "#64748b", // Slate-500
        background: "#0f172a", // Slate-900
        backgroundSecondary: "#1e293b", // Slate-800
        backgroundTertiary: "#334155", // Slate-700

        // Brand colors
        tint: tintColorDark,
        primary: "#a5b4fc", // Indigo-300
        primaryLight: "#312e81", // Indigo-900

        // Social platform colors (adjusted for dark mode)
        facebook: "#4267b2",
        instagram: "#c13584",
        twitter: "#1da1f2",

        // UI elements
        border: "#334155", // Slate-700
        borderLight: "#475569", // Slate-600
        card: "#1e293b", // Slate-800
        cardShadow: "rgba(0, 0, 0, 0.3)",

        // Status colors
        success: "#34d399", // Emerald-400
        warning: "#fbbf24", // Amber-400
        error: "#f87171", // Red-400

        // Tab navigation
        icon: "#94a3b8", // Slate-400
        tabIconDefault: "#64748b", // Slate-500
        tabIconSelected: tintColorDark,
        tabBackground: "rgba(15, 23, 42, 0.95)",
    },
};
