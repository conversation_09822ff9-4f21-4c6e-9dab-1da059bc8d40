import axios, { AxiosInstance } from 'axios';
import * as cheerio from 'cheerio';
import { <PERSON><PERSON><PERSON><PERSON> } from 'tough-cookie';

export interface InstagramCredentials {
  username: string;
  password: string;
}

export interface InstagramPost {
  id: string;
  shortcode: string;
  caption: string;
  imageUrl: string;
  videoUrl?: string;
  timestamp: Date;
  likes: number;
  comments: number;
  isVideo: boolean;
}

export interface InstagramProfile {
  username: string;
  fullName: string;
  biography: string;
  profilePicUrl: string;
  postsCount: number;
  followersCount: number;
  followingCount: number;
  isPrivate: boolean;
}

class InstagramScraper {
  private client: AxiosInstance;
  private cookieJar: CookieJar;
  private csrfToken: string = '';
  private isLoggedIn: boolean = false;
  private userId: string = '';

  constructor() {
    this.cookieJar = new CookieJar();
    this.client = axios.create({
      timeout: 30000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
    });

    // Add request interceptor to handle cookies
    this.client.interceptors.request.use((config) => {
      const cookies = this.cookieJar.getCookieStringSync('https://www.instagram.com');
      if (cookies) {
        config.headers['Cookie'] = cookies;
      }
      return config;
    });

    // Add response interceptor to save cookies
    this.client.interceptors.response.use((response) => {
      const setCookieHeader = response.headers['set-cookie'];
      if (setCookieHeader) {
        setCookieHeader.forEach((cookie: string) => {
          this.cookieJar.setCookieSync(cookie, 'https://www.instagram.com');
        });
      }
      return response;
    });
  }

  // Get initial page and extract CSRF token
  private async getInitialData(): Promise<void> {
    try {
      const response = await this.client.get('https://www.instagram.com/accounts/login/');
      const $ = cheerio.load(response.data);
      
      // Extract CSRF token
      const csrfToken = $('input[name="csrfmiddlewaretoken"]').attr('value') ||
                       $('meta[name="csrf-token"]').attr('content') ||
                       '';
      
      if (csrfToken) {
        this.csrfToken = csrfToken;
      }

      // Also try to extract from script tags
      const scriptTags = $('script').toArray();
      for (const script of scriptTags) {
        const content = $(script).html() || '';
        const csrfMatch = content.match(/"csrf_token":"([^"]+)"/);
        if (csrfMatch) {
          this.csrfToken = csrfMatch[1];
          break;
        }
      }
    } catch (error) {
      console.error('Failed to get initial data:', error);
      throw new Error('Failed to initialize Instagram session');
    }
  }

  // Login to Instagram
  async login(credentials: InstagramCredentials): Promise<boolean> {
    try {
      // Get initial page and CSRF token
      await this.getInitialData();

      if (!this.csrfToken) {
        throw new Error('Could not extract CSRF token');
      }

      // Prepare login data
      const loginData = new URLSearchParams({
        username: credentials.username,
        password: credentials.password,
        csrfmiddlewaretoken: this.csrfToken,
        optIntoOneTap: 'false',
        queryParams: '{}',
        trustedDeviceRecords: '{}',
      });

      // Perform login
      const loginResponse = await this.client.post(
        'https://www.instagram.com/accounts/login/ajax/',
        loginData,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': this.csrfToken,
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': 'https://www.instagram.com/accounts/login/',
          },
        }
      );

      const loginResult = loginResponse.data;

      if (loginResult.authenticated) {
        this.isLoggedIn = true;
        this.userId = loginResult.userId || '';
        return true;
      } else {
        console.error('Login failed:', loginResult);
        return false;
      }
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  }

  // Get user profile
  async getProfile(username?: string): Promise<InstagramProfile | null> {
    if (!this.isLoggedIn) {
      throw new Error('Not logged in');
    }

    try {
      const targetUsername = username || this.getLoggedInUsername();
      const response = await this.client.get(`https://www.instagram.com/${targetUsername}/`);
      const $ = cheerio.load(response.data);

      // Extract profile data from script tags
      const scriptTags = $('script').toArray();
      let profileData: any = null;

      for (const script of scriptTags) {
        const content = $(script).html() || '';
        const match = content.match(/window\._sharedData\s*=\s*({.+?});/);
        if (match) {
          try {
            const sharedData = JSON.parse(match[1]);
            const user = sharedData?.entry_data?.ProfilePage?.[0]?.graphql?.user;
            if (user) {
              profileData = user;
              break;
            }
          } catch (e) {
            // Continue to next script tag
          }
        }
      }

      if (!profileData) {
        throw new Error('Could not extract profile data');
      }

      return {
        username: profileData.username,
        fullName: profileData.full_name || '',
        biography: profileData.biography || '',
        profilePicUrl: profileData.profile_pic_url || '',
        postsCount: profileData.edge_owner_to_timeline_media?.count || 0,
        followersCount: profileData.edge_followed_by?.count || 0,
        followingCount: profileData.edge_follow?.count || 0,
        isPrivate: profileData.is_private || false,
      };
    } catch (error) {
      console.error('Failed to get profile:', error);
      return null;
    }
  }

  // Get user posts
  async getPosts(username?: string, count: number = 12): Promise<InstagramPost[]> {
    if (!this.isLoggedIn) {
      throw new Error('Not logged in');
    }

    try {
      const targetUsername = username || this.getLoggedInUsername();
      const response = await this.client.get(`https://www.instagram.com/${targetUsername}/`);
      const $ = cheerio.load(response.data);

      // Extract posts data from script tags
      const scriptTags = $('script').toArray();
      let postsData: any[] = [];

      for (const script of scriptTags) {
        const content = $(script).html() || '';
        const match = content.match(/window\._sharedData\s*=\s*({.+?});/);
        if (match) {
          try {
            const sharedData = JSON.parse(match[1]);
            const edges = sharedData?.entry_data?.ProfilePage?.[0]?.graphql?.user?.edge_owner_to_timeline_media?.edges;
            if (edges) {
              postsData = edges.slice(0, count);
              break;
            }
          } catch (e) {
            // Continue to next script tag
          }
        }
      }

      const posts: InstagramPost[] = postsData.map((edge: any) => {
        const node = edge.node;
        return {
          id: node.id,
          shortcode: node.shortcode,
          caption: node.edge_media_to_caption?.edges?.[0]?.node?.text || '',
          imageUrl: node.display_url || node.thumbnail_src || '',
          videoUrl: node.is_video ? node.video_url : undefined,
          timestamp: new Date(node.taken_at_timestamp * 1000),
          likes: node.edge_liked_by?.count || 0,
          comments: node.edge_media_to_comment?.count || 0,
          isVideo: node.is_video || false,
        };
      });

      return posts;
    } catch (error) {
      console.error('Failed to get posts:', error);
      return [];
    }
  }

  // Get feed posts (home timeline)
  async getFeedPosts(count: number = 12): Promise<InstagramPost[]> {
    if (!this.isLoggedIn) {
      throw new Error('Not logged in');
    }

    try {
      const response = await this.client.get('https://www.instagram.com/');
      const $ = cheerio.load(response.data);

      // Extract feed data from script tags
      const scriptTags = $('script').toArray();
      let feedData: any[] = [];

      for (const script of scriptTags) {
        const content = $(script).html() || '';
        const match = content.match(/window\._sharedData\s*=\s*({.+?});/);
        if (match) {
          try {
            const sharedData = JSON.parse(match[1]);
            const edges = sharedData?.entry_data?.FeedPage?.[0]?.graphql?.user?.edge_web_feed_timeline?.edges;
            if (edges) {
              feedData = edges.slice(0, count);
              break;
            }
          } catch (e) {
            // Continue to next script tag
          }
        }
      }

      const posts: InstagramPost[] = feedData.map((edge: any) => {
        const node = edge.node;
        return {
          id: node.id,
          shortcode: node.shortcode,
          caption: node.edge_media_to_caption?.edges?.[0]?.node?.text || '',
          imageUrl: node.display_url || node.thumbnail_src || '',
          videoUrl: node.is_video ? node.video_url : undefined,
          timestamp: new Date(node.taken_at_timestamp * 1000),
          likes: node.edge_liked_by?.count || 0,
          comments: node.edge_media_to_comment?.count || 0,
          isVideo: node.is_video || false,
        };
      });

      return posts;
    } catch (error) {
      console.error('Failed to get feed posts:', error);
      return [];
    }
  }

  // Check if logged in
  isAuthenticated(): boolean {
    return this.isLoggedIn;
  }

  // Get logged in username (you'd need to store this during login)
  private getLoggedInUsername(): string {
    // This would need to be stored during login process
    return '';
  }

  // Logout
  logout(): void {
    this.isLoggedIn = false;
    this.userId = '';
    this.csrfToken = '';
    this.cookieJar = new CookieJar();
  }
}

export const instagramScraper = new InstagramScraper();
