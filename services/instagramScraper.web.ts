// Web stub for Instagram scraper - doesn't use cheerio or other Node.js modules

export interface InstagramCredentials {
  username: string;
  password: string;
}

export interface InstagramPost {
  id: string;
  shortcode: string;
  caption: string;
  imageUrl: string;
  videoUrl?: string;
  timestamp: Date;
  likes: number;
  comments: number;
  isVideo: boolean;
}

export interface InstagramProfile {
  username: string;
  fullName: string;
  biography: string;
  profilePicUrl: string;
  postsCount: number;
  followersCount: number;
  followingCount: number;
  isPrivate: boolean;
}

class InstagramScraperWeb {
  private isLoggedIn: boolean = false;

  async login(credentials: InstagramCredentials): Promise<boolean> {
    // Web stub - always returns false since we can't scrape in browser
    console.warn('Instagram scraping not available in web browser');
    return false;
  }

  async getProfile(username?: string): Promise<InstagramProfile | null> {
    // Web stub
    return null;
  }

  async getPosts(username?: string, count: number = 12): Promise<InstagramPost[]> {
    // Web stub
    return [];
  }

  async getFeedPosts(count: number = 12): Promise<InstagramPost[]> {
    // Web stub
    return [];
  }

  isAuthenticated(): boolean {
    return this.isLoggedIn;
  }

  logout(): void {
    this.isLoggedIn = false;
  }
}

export const instagramScraper = new InstagramScraperWeb();
